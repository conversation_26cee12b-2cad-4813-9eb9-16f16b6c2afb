import torch
import torch.backends.cudnn as cudnn
import torchvision

import argparse
import os
from model_mobilvit import mobile_vit_xx_small as create_model
from light_cnns import *
from model import Net
import time
parser = argparse.ArgumentParser(description="Train on market1501")
parser.add_argument("--data-dir",default='data',type=str)
parser.add_argument("--no-cuda",action="store_true")
parser.add_argument("--gpu-id",default=0,type=int)
args = parser.parse_args()

# device
device = "cuda:{}".format(args.gpu_id) if torch.cuda.is_available() and not args.no_cuda else "cpu"
if torch.cuda.is_available() and not args.no_cuda:
    cudnn.benchmark = True

# data loader
root = args.data_dir
query_dir = os.path.join("/home/<USER>/mzc/shenduxuexi/yolov7-deepsort-tracking-master/VOC2007_L515/feature_extraction/feature_extraction_yuanshi/zhengchang/spilt_result_train")
transform = torchvision.transforms.Compose([
    # torchvision.transforms.Resize((128,64)),
    torchvision.transforms.Resize((128,64)),
    torchvision.transforms.ToTensor(),
    torchvision.transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
])
queryloader = torch.utils.data.DataLoader(
    torchvision.datasets.ImageFolder(query_dir, transform=transform),
    batch_size=1, shuffle=False
)
# net definition
net = create_model(3)
from thop import profile, clever_format
a=torch.randn(1,3,64,64)
flops, params = profile(net, inputs=(a,))
macs, params = clever_format([flops, params], "%.3f") # 格式化输出
print("Flops:",macs)

checkpoint = torch.load("/home/<USER>/mzc/shenduxuexi/yolov5-deepsort-pedestraintracking/deep_sort/configs/45_dilate_ghos_kuoda_95.751.t7")
net_dict = checkpoint['net_dict']
net.load_state_dict(net_dict, strict=False)
net.eval()
net.to(device)
num=0
zong_time=0.0
with torch.no_grad():
    for idx,(inputs,labels) in enumerate(queryloader):
        num=num+1
        inputs = inputs.to(device)
        start = time.time()
        features = net(inputs).to(device)
        print(time.time()-start)
        if num !=1:
            zong_time=zong_time+time.time()-start
    print(zong_time/(num+1))
    print(num)


