#!/usr/bin/env python3
"""
测试v19 CNN增强气吹系统的核心功能
"""

import torch
import numpy as np
import cv2
from v19_cnn_regressor_airflow_system import (
    LightweightCNNRegressor, 
    SpiralFeatureExtractor, 
    CNNRegressorTrainer
)

def test_cnn_regressor():
    """测试CNN回归器"""
    print("🤖 测试CNN回归器...")
    
    # 创建模型
    model = LightweightCNNRegressor(input_features=64)
    print(f"✅ CNN模型创建成功，参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 测试前向传播
    batch_size = 4
    feature_dim = 64
    test_input = torch.randn(batch_size, feature_dim)
    
    with torch.no_grad():
        output = model(test_input)
    
    print(f"✅ 前向传播测试成功")
    print(f"   输入形状: {test_input.shape}")
    print(f"   输出形状: {output.shape}")
    print(f"   输出范围: [{output.min():.3f}, {output.max():.3f}]")
    
    return model

def test_feature_extractor():
    """测试特征提取器"""
    print("\n📊 测试螺旋特征提取器...")
    
    extractor = SpiralFeatureExtractor(feature_dim=64)
    
    # 模拟螺旋路径特征
    mock_path_characteristics = {
        'stats': {
            'mean_gray': 128.5,
            'std_gray': 15.2,
            'min_gray': 80,
            'max_gray': 200,
            'mean_gradient': 12.3,
            'mean_variance': 25.6,
            'texture_ratio': 0.4,
            'valid_points_count': 150
        },
        'gray_values': list(range(100, 200, 2)),
        'gradients': [5.0 + i * 0.1 for i in range(20)],
        'local_variances': [10.0 + i * 0.5 for i in range(20)],
        'texture_regions': [(i, i+1) for i in range(0, 20, 2)],
        'smooth_regions': [(i, i+1) for i in range(1, 20, 2)],
        'valid_points': [(i, i+1) for i in range(50)]
    }
    
    mock_spiral_params = {
        'max_radius': 80.0,
        'step_size': 0.8,
        'angle_step': 0.12
    }
    
    mock_strawberry_shape = {
        'size': (60, 80),
        'aspect_ratio': 0.75,
        'circularity': 0.85,
        'extent': 0.7,
        'area': 3200
    }
    
    # 提取特征
    features = extractor.extract_features(
        mock_path_characteristics, 
        mock_spiral_params, 
        mock_strawberry_shape
    )
    
    print(f"✅ 特征提取成功")
    print(f"   特征维度: {len(features)}")
    print(f"   特征范围: [{features.min():.3f}, {features.max():.3f}]")
    print(f"   非零特征数: {np.count_nonzero(features)}")
    
    # 显示部分特征
    print(f"   前10个特征: {features[:10]}")
    
    return features

def test_trainer():
    """测试训练器"""
    print("\n🎓 测试CNN训练器...")
    
    trainer = CNNRegressorTrainer(model_save_path="test_models/test_cnn.pth")
    
    # 生成模拟训练数据
    print("生成模拟训练数据...")
    extractor = SpiralFeatureExtractor(feature_dim=64)
    
    for i in range(25):  # 生成25个训练样本
        # 模拟不同的螺旋路径特征
        texture_ratio = np.random.uniform(0.1, 0.8)
        mean_variance = np.random.uniform(10, 50)
        
        mock_characteristics = {
            'stats': {
                'mean_gray': np.random.uniform(80, 180),
                'std_gray': np.random.uniform(5, 30),
                'min_gray': np.random.uniform(50, 100),
                'max_gray': np.random.uniform(150, 255),
                'mean_gradient': np.random.uniform(5, 25),
                'mean_variance': mean_variance,
                'texture_ratio': texture_ratio,
                'valid_points_count': np.random.randint(50, 200)
            },
            'gray_values': [np.random.randint(50, 255) for _ in range(50)],
            'gradients': [np.random.uniform(0, 30) for _ in range(20)],
            'local_variances': [np.random.uniform(5, 60) for _ in range(20)],
            'texture_regions': [(j, j+1) for j in range(0, 10, 2)],
            'smooth_regions': [(j, j+1) for j in range(1, 10, 2)],
            'valid_points': [(j, j+1) for j in range(30)]
        }
        
        mock_spiral_params = {
            'max_radius': np.random.uniform(60, 120),
            'step_size': np.random.uniform(0.5, 1.2),
            'angle_step': np.random.uniform(0.08, 0.16)
        }
        
        mock_shape_info = {
            'size': (np.random.randint(40, 100), np.random.randint(40, 100)),
            'aspect_ratio': np.random.uniform(0.5, 2.0),
            'circularity': np.random.uniform(0.3, 1.0),
            'extent': np.random.uniform(0.4, 0.9),
            'area': np.random.randint(1000, 8000)
        }
        
        initial_occlusion = np.random.uniform(0.1, 0.8)
        
        # 模拟残留遮挡率（基于纹理复杂度）
        complexity_factor = texture_ratio * 0.5 + (mean_variance / 50.0) * 0.5
        base_reduction = 0.2 + complexity_factor * 0.6
        simulated_residual = max(0.0, initial_occlusion * (1 - base_reduction))
        
        trainer.collect_training_sample(
            mock_characteristics, mock_spiral_params, mock_shape_info,
            initial_occlusion, simulated_residual
        )
    
    print(f"✅ 收集了 {len(trainer.training_data)} 个训练样本")
    
    # 训练模型
    print("开始训练CNN模型...")
    trained_model = trainer.train_model(epochs=20, batch_size=8, learning_rate=0.001)
    
    if trained_model:
        print("✅ CNN模型训练成功")
        
        # 测试预测
        test_features = extractor.extract_features(
            trainer.training_data[0]['features'] if hasattr(trainer.training_data[0], 'features') else mock_characteristics,
            mock_spiral_params,
            mock_shape_info
        )
        
        test_tensor = torch.FloatTensor(test_features).unsqueeze(0)
        with torch.no_grad():
            prediction = trained_model(test_tensor).item()
        
        print(f"✅ 预测测试成功: {prediction:.3f}")
        
        return trained_model
    else:
        print("❌ CNN模型训练失败")
        return None

def test_integration():
    """集成测试"""
    print("\n🔧 集成测试...")
    
    # 测试所有组件协同工作
    model = test_cnn_regressor()
    features = test_feature_extractor()
    trained_model = test_trainer()
    
    if model and features is not None and trained_model:
        print("✅ 所有组件测试通过")
        
        # 测试完整的预测流程
        test_tensor = torch.FloatTensor(features).unsqueeze(0)
        with torch.no_grad():
            prediction = trained_model(test_tensor).item()
        
        print(f"✅ 完整预测流程测试成功")
        print(f"   输入特征维度: {len(features)}")
        print(f"   预测残留遮挡率: {prediction:.3f} ({prediction*100:.1f}%)")
        
        return True
    else:
        print("❌ 集成测试失败")
        return False

def main():
    """主测试函数"""
    print("=" * 80)
    print("🤖 v19 CNN增强气吹系统功能测试")
    print("=" * 80)
    
    try:
        # 创建测试目录
        import os
        os.makedirs("test_models", exist_ok=True)
        
        # 运行所有测试
        success = test_integration()
        
        print("\n" + "=" * 80)
        if success:
            print("🎉 所有测试通过！v19系统功能正常")
            print("✅ CNN回归器可以正常预测残留遮挡率")
            print("✅ 特征提取器可以正常处理螺旋路径特征")
            print("✅ 训练器可以正常训练和保存模型")
        else:
            print("❌ 部分测试失败，请检查代码")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
