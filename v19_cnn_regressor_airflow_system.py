import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import cv2
import os
import numpy as np
import math
import pickle
from datetime import datetime
from deep_sort.deep_sort import DeepSort
from deep_sort.configs.parser import get_config
from ultralytics import YOLO
from collections import defaultdict


class LightweightCNNRegressor(nn.Module):
    """
    轻量级CNN回归器
    输入：螺旋路径特征
    输出：预测的气吹后残留遮挡率
    """
    
    def __init__(self, input_features=64):
        super(LightweightCNNRegressor, self).__init__()
        
        # 特征提取层
        self.feature_extractor = nn.Sequential(
            # 第一层卷积
            nn.Conv1d(1, 16, kernel_size=3, padding=1),
            nn.BatchNorm1d(16),
            nn.ReLU(inplace=True),
            nn.MaxPool1d(2),
            
            # 第二层卷积
            nn.Conv1d(16, 32, kernel_size=3, padding=1),
            nn.BatchNorm1d(32),
            nn.ReLU(inplace=True),
            nn.MaxPool1d(2),
            
            # 第三层卷积
            nn.Conv1d(32, 64, kernel_size=3, padding=1),
            nn.BatchNorm1d(64),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool1d(8)  # 自适应池化到固定大小
        )
        
        # 回归头
        self.regressor = nn.Sequential(
            nn.Flatten(),
            nn.Linear(64 * 8, 128),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(128, 64),
            nn.ReLU(inplace=True),
            nn.Dropout(0.2),
            nn.Linear(64, 1),
            nn.Sigmoid()  # 输出0-1之间的残留遮挡率
        )
        
    def forward(self, x):
        """
        前向传播
        x: (batch_size, sequence_length) - 螺旋路径特征序列
        """
        # 添加通道维度
        x = x.unsqueeze(1)  # (batch_size, 1, sequence_length)
        
        # 特征提取
        features = self.feature_extractor(x)
        
        # 回归预测
        residual_occlusion = self.regressor(features)
        
        return residual_occlusion.squeeze(-1)  # (batch_size,)


class SpiralFeatureExtractor:
    """
    螺旋路径特征提取器
    将螺旋路径分析结果转换为CNN可用的特征向量
    """
    
    def __init__(self, feature_dim=64):
        self.feature_dim = feature_dim
        
    def extract_features(self, path_characteristics, spiral_params, strawberry_shape_info):
        """
        从螺旋路径特征中提取固定长度的特征向量
        """
        features = np.zeros(self.feature_dim)
        
        try:
            if 'stats' in path_characteristics:
                stats = path_characteristics['stats']
                
                # 基础统计特征 (0-9)
                features[0] = stats.get('mean_gray', 0) / 255.0  # 归一化到0-1
                features[1] = stats.get('std_gray', 0) / 255.0
                features[2] = stats.get('min_gray', 0) / 255.0
                features[3] = stats.get('max_gray', 0) / 255.0
                features[4] = stats.get('mean_gradient', 0) / 100.0  # 假设最大梯度100
                features[5] = stats.get('mean_variance', 0) / 1000.0  # 假设最大方差1000
                features[6] = stats.get('texture_ratio', 0)
                features[7] = min(stats.get('valid_points_count', 0) / 1000.0, 1.0)  # 归一化
                
                # 灰度分布特征 (8-15)
                gray_values = path_characteristics.get('gray_values', [])
                if gray_values:
                    gray_hist, _ = np.histogram(gray_values, bins=8, range=(0, 255))
                    gray_hist = gray_hist / (len(gray_values) + 1e-6)  # 归一化
                    features[8:16] = gray_hist
                
                # 梯度分布特征 (16-23)
                gradients = path_characteristics.get('gradients', [])
                if gradients:
                    grad_hist, _ = np.histogram(gradients, bins=8, range=(0, 50))
                    grad_hist = grad_hist / (len(gradients) + 1e-6)
                    features[16:24] = grad_hist
                
                # 方差分布特征 (24-31)
                variances = path_characteristics.get('local_variances', [])
                if variances:
                    var_hist, _ = np.histogram(variances, bins=8, range=(0, 100))
                    var_hist = var_hist / (len(variances) + 1e-6)
                    features[24:32] = var_hist
            
            # 螺旋参数特征 (32-39)
            if spiral_params:
                features[32] = min(spiral_params.get('max_radius', 0) / 200.0, 1.0)
                features[33] = spiral_params.get('step_size', 0) / 2.0
                features[34] = spiral_params.get('angle_step', 0) / 1.0
                
            # 草莓形状特征 (35-43)
            if strawberry_shape_info:
                features[35] = min(strawberry_shape_info.get('aspect_ratio', 1.0) / 3.0, 1.0)
                features[36] = strawberry_shape_info.get('circularity', 0)
                features[37] = strawberry_shape_info.get('extent', 0)
                features[38] = min(strawberry_shape_info.get('area', 0) / 10000.0, 1.0)
                
                size = strawberry_shape_info.get('size', (0, 0))
                features[39] = min(size[0] / 200.0, 1.0)  # 宽度
                features[40] = min(size[1] / 200.0, 1.0)  # 高度
            
            # 纹理区域分布特征 (41-48)
            texture_regions = path_characteristics.get('texture_regions', [])
            smooth_regions = path_characteristics.get('smooth_regions', [])
            
            if texture_regions or smooth_regions:
                total_regions = len(texture_regions) + len(smooth_regions)
                if total_regions > 0:
                    features[41] = len(texture_regions) / total_regions
                    features[42] = len(smooth_regions) / total_regions
            
            # 空间分布特征 (43-50)
            valid_points = path_characteristics.get('valid_points', [])
            if valid_points and len(valid_points) > 4:
                # 计算点的空间分布
                points_array = np.array(valid_points)
                x_coords = points_array[:, 0]
                y_coords = points_array[:, 1]
                
                features[43] = np.std(x_coords) / 100.0  # X方向分散度
                features[44] = np.std(y_coords) / 100.0  # Y方向分散度
                features[45] = (np.max(x_coords) - np.min(x_coords)) / 200.0  # X范围
                features[46] = (np.max(y_coords) - np.min(y_coords)) / 200.0  # Y范围
            
            # 预留特征位 (47-63)
            # 可以根据需要添加更多特征
            
        except Exception as e:
            print(f"特征提取错误: {e}")
            
        return features


class CNNRegressorTrainer:
    """
    CNN回归器训练器
    负责收集训练数据和训练模型
    """
    
    def __init__(self, model_save_path="models/cnn_regressor.pth"):
        self.model_save_path = model_save_path
        self.training_data = []
        self.feature_extractor = SpiralFeatureExtractor()
        
        # 创建模型保存目录
        os.makedirs(os.path.dirname(model_save_path), exist_ok=True)
        
    def collect_training_sample(self, path_characteristics, spiral_params, 
                              strawberry_shape_info, initial_occlusion_ratio, 
                              simulated_residual_ratio=None):
        """
        收集训练样本
        """
        features = self.feature_extractor.extract_features(
            path_characteristics, spiral_params, strawberry_shape_info
        )
        
        # 如果没有提供真实的残留遮挡率，使用简单的模拟
        if simulated_residual_ratio is None:
            # 简单模拟：基于初始遮挡率和特征计算残留率
            texture_ratio = path_characteristics.get('stats', {}).get('texture_ratio', 0.5)
            complexity_factor = min(texture_ratio * 2, 1.0)
            
            # 假设气吹效果：纹理复杂度越高，残留率越高
            base_reduction = 0.3 + complexity_factor * 0.4  # 30%-70%的去除效果
            simulated_residual_ratio = max(0.0, initial_occlusion_ratio * (1 - base_reduction))
        
        sample = {
            'features': features,
            'initial_occlusion': initial_occlusion_ratio,
            'residual_occlusion': simulated_residual_ratio
        }
        
        self.training_data.append(sample)
        print(f"收集训练样本: 初始遮挡{initial_occlusion_ratio:.3f} -> 残留遮挡{simulated_residual_ratio:.3f}")
        
    def train_model(self, epochs=100, batch_size=16, learning_rate=0.001):
        """
        训练CNN回归器
        """
        if len(self.training_data) < 10:
            print(f"训练数据不足 ({len(self.training_data)}), 需要至少10个样本")
            return None
            
        print(f"开始训练CNN回归器，样本数: {len(self.training_data)}")
        
        # 准备训练数据
        features = np.array([sample['features'] for sample in self.training_data])
        targets = np.array([sample['residual_occlusion'] for sample in self.training_data])
        
        # 转换为PyTorch张量
        X = torch.FloatTensor(features)
        y = torch.FloatTensor(targets)
        
        # 创建数据集
        dataset = torch.utils.data.TensorDataset(X, y)
        dataloader = torch.utils.data.DataLoader(dataset, batch_size=batch_size, shuffle=True)
        
        # 创建模型
        model = LightweightCNNRegressor(input_features=features.shape[1])
        criterion = nn.MSELoss()
        optimizer = optim.Adam(model.parameters(), lr=learning_rate)
        
        # 训练循环
        model.train()
        for epoch in range(epochs):
            total_loss = 0.0
            for batch_features, batch_targets in dataloader:
                optimizer.zero_grad()
                
                predictions = model(batch_features)
                loss = criterion(predictions, batch_targets)
                
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
            
            if (epoch + 1) % 20 == 0:
                avg_loss = total_loss / len(dataloader)
                print(f"Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.6f}")
        
        # 保存模型
        torch.save({
            'model_state_dict': model.state_dict(),
            'feature_dim': features.shape[1],
            'training_samples': len(self.training_data)
        }, self.model_save_path)
        
        print(f"模型已保存到: {self.model_save_path}")
        return model


class SpiralGuidedCNNAirflowSystem:
    """
    螺旋引导区域生长 + CNN回归器智能气吹控制系统
    核心创新：
    1. 螺旋搜索路径生成
    2. 螺旋路径引导的区域生长
    3. 轻量级CNN回归器预测残留遮挡率
    4. 基于预测结果优化气吹参数
    5. 动态参数调整
    6. 最佳气吹起点确定
    """
    
    def __init__(self, config):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = YOLO(config["weights"]).to(self.device)
        self.class_names = self.model.names

        # DeepSORT初始化
        cfg = get_config()
        cfg.merge_from_file("deep_sort/configs/deep_sort.yaml")
        
        cfg.DEEPSORT.MAX_DIST = 0.3
        cfg.DEEPSORT.MIN_CONFIDENCE = 0.4
        cfg.DEEPSORT.NMS_MAX_OVERLAP = 0.8
        cfg.DEEPSORT.MAX_IOU_DISTANCE = 0.9
        cfg.DEEPSORT.MAX_AGE = 50
        cfg.DEEPSORT.N_INIT = 5
        cfg.DEEPSORT.NN_BUDGET = 150
        
        self.tracker = DeepSort(
            model_path=cfg.DEEPSORT.REID_CKPT,
            max_dist=cfg.DEEPSORT.MAX_DIST,
            min_confidence=cfg.DEEPSORT.MIN_CONFIDENCE,
            nms_max_overlap=cfg.DEEPSORT.NMS_MAX_OVERLAP,
            max_iou_distance=cfg.DEEPSORT.MAX_IOU_DISTANCE,
            max_age=cfg.DEEPSORT.MAX_AGE,
            n_init=cfg.DEEPSORT.N_INIT,
            nn_budget=cfg.DEEPSORT.NN_BUDGET,
            use_cuda=torch.cuda.is_available()
        )

        self.input_path = config["input"]
        self.output_path = config["output"]
        os.makedirs(os.path.dirname(self.output_path), exist_ok=True)

        # 跟踪参数
        self.color_dict = {}
        self.alpha = 0.3
        self.border_margin = 40
        self.valid_region = None

        # 统计相关
        self.recorded_ids = set()
        self.total_count = 0
        self.strawberry_analysis_results = {}
        
        # ID稳定性跟踪
        self.track_history = {}
        self.id_mapping = {}
        self.next_stable_id = 1

        # 螺旋引导区域生长参数
        self.spiral_params = {
            'max_radius': 100,
            'step_size': 0.8,
            'angle_step': 0.12,
            'max_steps': 800
        }
        
        self.region_growing_params = {
            'base_threshold': 15,
            'max_diff_from_seed': 30,
            'min_region_size': 50,
            'max_region_size': 5000
        }
        
        # CNN回归器相关
        self.cnn_regressor = None
        self.feature_extractor = SpiralFeatureExtractor()
        self.trainer = CNNRegressorTrainer()
        self.load_pretrained_model()

    def load_pretrained_model(self):
        """加载预训练的CNN回归器"""
        try:
            if os.path.exists(self.trainer.model_save_path):
                checkpoint = torch.load(self.trainer.model_save_path, map_location=self.device)
                self.cnn_regressor = LightweightCNNRegressor(
                    input_features=checkpoint['feature_dim']
                ).to(self.device)
                self.cnn_regressor.load_state_dict(checkpoint['model_state_dict'])
                self.cnn_regressor.eval()
                print(f"✅ 加载预训练CNN回归器: {checkpoint['training_samples']}个训练样本")
            else:
                print("⚠️ 未找到预训练模型，将在处理过程中收集数据并训练")
        except Exception as e:
            print(f"❌ 加载预训练模型失败: {e}")
            self.cnn_regressor = None

    def predict_residual_occlusion(self, path_characteristics, spiral_params, strawberry_shape_info):
        """
        使用CNN回归器预测气吹后的残留遮挡率
        """
        try:
            if self.cnn_regressor is None:
                # 如果没有训练好的模型，使用简单的启发式方法
                return self.heuristic_residual_prediction(path_characteristics)

            # 提取特征
            features = self.feature_extractor.extract_features(
                path_characteristics, spiral_params, strawberry_shape_info
            )

            # 转换为张量并预测
            features_tensor = torch.FloatTensor(features).unsqueeze(0).to(self.device)

            with torch.no_grad():
                predicted_residual = self.cnn_regressor(features_tensor).item()

            print(f"  🤖 CNN预测残留遮挡率: {predicted_residual:.3f}")
            return predicted_residual

        except Exception as e:
            print(f"CNN预测失败: {e}, 使用启发式方法")
            return self.heuristic_residual_prediction(path_characteristics)

    def heuristic_residual_prediction(self, path_characteristics):
        """
        启发式残留遮挡率预测（当CNN模型不可用时）
        """
        if 'stats' not in path_characteristics:
            return 0.3  # 默认残留30%

        stats = path_characteristics['stats']
        texture_ratio = stats.get('texture_ratio', 0.5)
        mean_variance = stats.get('mean_variance', 20)
        mean_gradient = stats.get('mean_gradient', 10)

        # 基于纹理复杂度的启发式预测
        complexity_score = (texture_ratio * 0.4 +
                          min(mean_variance / 50.0, 1.0) * 0.3 +
                          min(mean_gradient / 30.0, 1.0) * 0.3)

        # 复杂度越高，残留率越高
        residual_ratio = 0.1 + complexity_score * 0.6  # 10%-70%

        print(f"  📊 启发式预测残留遮挡率: {residual_ratio:.3f} (复杂度: {complexity_score:.3f})")
        return residual_ratio

    def optimize_airflow_parameters(self, initial_occlusion_ratio, predicted_residual_ratio,
                                  strawberry_area, obstacle_area):
        """
        基于CNN预测结果优化气吹参数
        """
        # 计算预期的遮挡去除效果
        expected_removal_ratio = 1.0 - (predicted_residual_ratio / max(initial_occlusion_ratio, 0.001))

        # 基于预期效果调整气吹强度
        if expected_removal_ratio > 0.8:  # 预期效果很好
            intensity_multiplier = 0.8  # 降低强度节省能源
        elif expected_removal_ratio > 0.5:  # 预期效果一般
            intensity_multiplier = 1.0  # 标准强度
        else:  # 预期效果较差
            intensity_multiplier = 1.3  # 增加强度

        # 计算优化后的气吹强度
        base_intensity = min(initial_occlusion_ratio * 100, 100)
        optimized_intensity = min(base_intensity * intensity_multiplier, 100)

        # 计算气吹持续时间（基于面积和预期效果）
        area_factor = min(obstacle_area / 1000.0, 2.0)  # 面积因子
        duration = max(1.0, area_factor * (2.0 - expected_removal_ratio))  # 1-4秒

        print(f"  ⚙️ 气吹参数优化:")
        print(f"    预期去除效果: {expected_removal_ratio:.1%}")
        print(f"    强度调整: {base_intensity:.1f}% -> {optimized_intensity:.1f}%")
        print(f"    建议持续时间: {duration:.1f}秒")

        return {
            'optimized_intensity': optimized_intensity,
            'expected_removal_ratio': expected_removal_ratio,
            'duration': duration,
            'intensity_multiplier': intensity_multiplier
        }

    def set_valid_region(self, frame_width):
        """设置有效区域边界"""
        self.valid_region = (self.border_margin, frame_width - self.border_margin)

    def is_in_valid_region(self, x_center):
        """检查目标是否在有效区域内"""
        return self.valid_region[0] <= x_center <= self.valid_region[1]

    def get_stable_id(self, track_id, position, frame_count):
        """获取稳定的ID，减少ID切换"""
        x_center, y_center = position

        if track_id in self.id_mapping:
            stable_id = self.id_mapping[track_id]
            if stable_id in self.track_history:
                self.track_history[stable_id]['positions'].append((x_center, y_center))
                self.track_history[stable_id]['last_seen'] = frame_count
                if len(self.track_history[stable_id]['positions']) > 20:
                    self.track_history[stable_id]['positions'] = self.track_history[stable_id]['positions'][-20:]
            return stable_id

        # 检查是否有相近位置的历史轨迹
        min_distance = float('inf')
        best_stable_id = None
        distance_threshold = 100

        for stable_id, history in self.track_history.items():
            if frame_count - history['last_seen'] < 30:
                if history['positions']:
                    last_pos = history['positions'][-1]
                    distance = np.sqrt((x_center - last_pos[0])**2 + (y_center - last_pos[1])**2)
                    if distance < distance_threshold and distance < min_distance:
                        min_distance = distance
                        best_stable_id = stable_id

        if best_stable_id is not None:
            self.id_mapping[track_id] = best_stable_id
            self.track_history[best_stable_id]['positions'].append((x_center, y_center))
            self.track_history[best_stable_id]['last_seen'] = frame_count
            return best_stable_id
        else:
            stable_id = self.next_stable_id
            self.next_stable_id += 1
            self.id_mapping[track_id] = stable_id
            self.track_history[stable_id] = {
                'positions': [(x_center, y_center)],
                'last_seen': frame_count
            }
            return stable_id

    # ==================== 螺旋搜索算法 ====================

    def generate_adaptive_spiral_path(self, center_point, strawberry_shape_info=None):
        """
        生成自适应螺旋搜索路径
        根据草莓形状和位置动态调整螺旋参数
        """
        center_x, center_y = center_point
        spiral_path = [center_point]

        # 根据草莓形状调整螺旋参数
        if strawberry_shape_info:
            width, height = strawberry_shape_info.get('size', (80, 80))
            aspect_ratio = width / height if height > 0 else 1.0

            # 根据长宽比调整螺旋形状
            if aspect_ratio > 1.3:  # 横向草莓
                max_radius = max(self.spiral_params['max_radius'], width * 0.8)
                step_size = self.spiral_params['step_size'] * 0.8
                angle_step = self.spiral_params['angle_step'] * 1.2
            elif aspect_ratio < 0.7:  # 纵向草莓
                max_radius = max(self.spiral_params['max_radius'], height * 0.8)
                step_size = self.spiral_params['step_size'] * 1.2
                angle_step = self.spiral_params['angle_step'] * 0.8
            else:  # 圆形草莓
                max_radius = self.spiral_params['max_radius']
                step_size = self.spiral_params['step_size']
                angle_step = self.spiral_params['angle_step']
        else:
            max_radius = self.spiral_params['max_radius']
            step_size = self.spiral_params['step_size']
            angle_step = self.spiral_params['angle_step']

        theta = 0.0
        radius = 0.0

        for step in range(self.spiral_params['max_steps']):
            radius += step_size / (2 * np.pi) * 2.0

            if radius > max_radius:
                break

            theta += angle_step

            # 计算螺旋点坐标
            x = int(center_x + radius * np.cos(theta))
            y = int(center_y + radius * np.sin(theta))

            spiral_path.append((x, y))

        print(f"  🌀 生成螺旋路径: {len(spiral_path)}个点, 最大半径: {max_radius:.1f}")
        return spiral_path, {'max_radius': max_radius, 'step_size': step_size, 'angle_step': angle_step}

    def analyze_spiral_path_characteristics(self, image, spiral_path):
        """
        分析螺旋路径上点的特性
        为区域生长和CNN预测提供特征
        """
        if len(image.shape) == 3:
            gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray_image = image.copy()

        height, width = gray_image.shape

        # 分析螺旋路径上的像素特性
        path_characteristics = {
            'gray_values': [],
            'gradients': [],
            'local_variances': [],
            'valid_points': [],
            'texture_regions': [],
            'smooth_regions': []
        }

        for i, (x, y) in enumerate(spiral_path):
            if 0 <= x < width and 0 <= y < height:
                # 灰度值
                gray_val = int(gray_image[y, x])
                path_characteristics['gray_values'].append(gray_val)
                path_characteristics['valid_points'].append((x, y))

                # 计算局部梯度
                if 1 <= x < width-1 and 1 <= y < height-1:
                    gx = int(gray_image[y, x+1]) - int(gray_image[y, x-1])
                    gy = int(gray_image[y+1, x]) - int(gray_image[y-1, x])
                    gradient = np.sqrt(gx*gx + gy*gy)
                    path_characteristics['gradients'].append(gradient)

                    # 计算局部方差
                    local_window = gray_image[y-1:y+2, x-1:x+2]
                    local_var = np.var(local_window)
                    path_characteristics['local_variances'].append(local_var)

                    # 分类纹理区域和平滑区域
                    if local_var > 20:
                        path_characteristics['texture_regions'].append((x, y))
                    else:
                        path_characteristics['smooth_regions'].append((x, y))

        # 计算统计信息
        if path_characteristics['gray_values']:
            stats = {
                'mean_gray': np.mean(path_characteristics['gray_values']),
                'std_gray': np.std(path_characteristics['gray_values']),
                'min_gray': np.min(path_characteristics['gray_values']),
                'max_gray': np.max(path_characteristics['gray_values']),
                'mean_gradient': np.mean(path_characteristics['gradients']) if path_characteristics['gradients'] else 0,
                'mean_variance': np.mean(path_characteristics['local_variances']) if path_characteristics['local_variances'] else 0,
                'texture_ratio': len(path_characteristics['texture_regions']) / len(path_characteristics['valid_points']) if path_characteristics['valid_points'] else 0,
                'valid_points_count': len(path_characteristics['valid_points'])
            }

            print(f"  📊 螺旋路径分析: 有效点{stats['valid_points_count']}, 平均灰度{stats['mean_gray']:.1f}")
            print(f"  📊 纹理比例: {stats['texture_ratio']:.2f}, 平均梯度: {stats['mean_gradient']:.1f}")

            path_characteristics['stats'] = stats

        return path_characteristics

    def dynamic_parameter_adjustment(self, path_characteristics):
        """
        根据螺旋路径特性动态调整区域生长参数
        """
        if 'stats' not in path_characteristics:
            return self.region_growing_params.copy()

        stats = path_characteristics['stats']
        adjusted_params = self.region_growing_params.copy()

        # 根据灰度标准差调整阈值
        if stats['std_gray'] < 8:  # 均匀区域
            adjusted_params['base_threshold'] = max(8, self.region_growing_params['base_threshold'] - 5)
            adjusted_params['max_diff_from_seed'] = max(15, self.region_growing_params['max_diff_from_seed'] - 10)
            print(f"  🎯 均匀区域: 降低阈值到 {adjusted_params['base_threshold']}")
        elif stats['std_gray'] > 25:  # 复杂纹理区域
            adjusted_params['base_threshold'] = min(30, self.region_growing_params['base_threshold'] + 8)
            adjusted_params['max_diff_from_seed'] = min(50, self.region_growing_params['max_diff_from_seed'] + 15)
            print(f"  🎯 纹理区域: 提高阈值到 {adjusted_params['base_threshold']}")

        # 根据纹理比例调整
        if stats['texture_ratio'] > 0.6:  # 高纹理区域
            adjusted_params['base_threshold'] += 5
            adjusted_params['max_region_size'] = int(self.region_growing_params['max_region_size'] * 1.5)
            print(f"  🎯 高纹理: 扩大最大区域到 {adjusted_params['max_region_size']}")
        elif stats['texture_ratio'] < 0.2:  # 低纹理区域
            adjusted_params['base_threshold'] -= 3
            adjusted_params['min_region_size'] = max(20, self.region_growing_params['min_region_size'] - 20)
            print(f"  🎯 低纹理: 降低最小区域到 {adjusted_params['min_region_size']}")

        # 根据平均梯度调整
        if stats['mean_gradient'] > 15:  # 高梯度区域
            adjusted_params['base_threshold'] += 3
            print(f"  🎯 高梯度: 调整阈值到 {adjusted_params['base_threshold']}")

        return adjusted_params

    def spiral_guided_region_growing(self, image, spiral_path, path_characteristics):
        """
        螺旋引导的区域生长算法
        使用螺旋路径上的点作为种子点进行区域生长
        """
        if len(image.shape) == 3:
            gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray_image = image.copy()

        height, width = gray_image.shape

        # 动态调整参数
        adjusted_params = self.dynamic_parameter_adjustment(path_characteristics)

        # 初始化结果掩膜
        grown_mask = np.zeros((height, width), dtype=np.uint8)
        visited = np.zeros((height, width), dtype=bool)

        # 选择螺旋路径上的优质种子点
        seed_points = self.select_optimal_seed_points(spiral_path, path_characteristics, gray_image)

        print(f"  🌱 选择了 {len(seed_points)} 个优质种子点进行区域生长")

        # 4邻域
        neighbors = [(0, 1), (1, 0), (0, -1), (-1, 0)]
        total_grown_pixels = 0

        for seed_idx, seed_point in enumerate(seed_points):
            if total_grown_pixels >= adjusted_params['max_region_size']:
                break

            seed_x, seed_y = seed_point
            if visited[seed_y, seed_x]:
                continue

            # 种子点灰度值
            seed_gray = int(gray_image[seed_y, seed_x])

            # 区域生长队列
            queue = [seed_point]
            region_pixels = []

            while queue and len(region_pixels) < adjusted_params['max_region_size']:
                current_x, current_y = queue.pop(0)

                if visited[current_y, current_x]:
                    continue

                visited[current_y, current_x] = True
                current_gray = int(gray_image[current_y, current_x])

                # 检查与种子点的差异
                if abs(current_gray - seed_gray) <= adjusted_params['max_diff_from_seed']:
                    region_pixels.append((current_x, current_y))
                    grown_mask[current_y, current_x] = 255

                    # 添加邻居到队列
                    for dx, dy in neighbors:
                        neighbor_x, neighbor_y = current_x + dx, current_y + dy

                        if (0 <= neighbor_x < width and 0 <= neighbor_y < height and
                            not visited[neighbor_y, neighbor_x]):

                            neighbor_gray = int(gray_image[neighbor_y, neighbor_x])
                            diff = abs(current_gray - neighbor_gray)

                            if diff <= adjusted_params['base_threshold']:
                                queue.append((neighbor_x, neighbor_y))

            # 检查区域大小
            if len(region_pixels) >= adjusted_params['min_region_size']:
                total_grown_pixels += len(region_pixels)
                print(f"    种子{seed_idx+1}: 生长了 {len(region_pixels)} 像素")
            else:
                # 移除太小的区域
                for px, py in region_pixels:
                    grown_mask[py, px] = 0

        # 形态学后处理
        if total_grown_pixels > 0:
            kernel = np.ones((3, 3), np.uint8)
            grown_mask = cv2.morphologyEx(grown_mask, cv2.MORPH_CLOSE, kernel)
            grown_mask = cv2.morphologyEx(grown_mask, cv2.MORPH_OPEN, kernel)

            print(f"  ✅ 螺旋引导区域生长完成: 总共生长 {total_grown_pixels} 像素")
        else:
            print(f"  ❌ 区域生长失败，使用备用方案")
            # 备用方案：在螺旋路径中心创建小区域
            if spiral_path:
                center_x, center_y = spiral_path[0]
                cv2.circle(grown_mask, (center_x, center_y), 20, 255, -1)

        return grown_mask, adjusted_params, total_grown_pixels

    def select_optimal_seed_points(self, spiral_path, path_characteristics, gray_image):
        """
        从螺旋路径中选择最佳种子点
        """
        if not path_characteristics.get('valid_points'):
            return spiral_path[:5]  # 备用方案

        valid_points = path_characteristics['valid_points']
        gray_values = path_characteristics['gray_values']
        local_variances = path_characteristics.get('local_variances', [])

        # 计算每个点的适合度分数
        seed_candidates = []

        for i, (point, gray_val) in enumerate(zip(valid_points, gray_values)):
            score = 0

            # 基于局部方差的分数（低方差更好）
            if i < len(local_variances):
                variance_score = max(0, 20 - local_variances[i]) / 20
                score += variance_score * 0.4

            # 基于灰度值的分数（中等灰度更好）
            gray_score = 1.0 - abs(gray_val - 128) / 128
            score += gray_score * 0.3

            # 基于位置的分数（螺旋中心附近更好）
            position_score = max(0, 1.0 - i / len(valid_points))
            score += position_score * 0.3

            seed_candidates.append((point, score))

        # 按分数排序并选择前几个
        seed_candidates.sort(key=lambda x: x[1], reverse=True)

        # 选择分数最高的种子点，但要保持一定间距
        selected_seeds = []
        min_distance = 15  # 最小间距

        for point, score in seed_candidates:
            if len(selected_seeds) >= 8:  # 最多8个种子点
                break

            # 检查与已选种子点的距离
            too_close = False
            for selected_point in selected_seeds:
                distance = np.sqrt((point[0] - selected_point[0])**2 + (point[1] - selected_point[1])**2)
                if distance < min_distance:
                    too_close = True
                    break

            if not too_close:
                selected_seeds.append(point)

        return selected_seeds if selected_seeds else valid_points[:5]

    # ==================== 叶片分析和气吹参数计算 ====================

    def find_strawberry_center(self, obstacle_mask):
        """从遮挡掩膜中找到草莓的中心点"""
        _, binary = cv2.threshold(obstacle_mask, 127, 255, cv2.THRESH_BINARY)

        kernel = np.ones((5, 5), np.uint8)
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)

        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            h, w = obstacle_mask.shape
            return (w // 2, h // 2)

        largest_contour = max(contours, key=cv2.contourArea)
        M = cv2.moments(largest_contour)

        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            h, w = obstacle_mask.shape
            cx = max(0, min(cx, w-1))
            cy = max(0, min(cy, h-1))
            return (cx, cy)
        else:
            x, y, w, h = cv2.boundingRect(largest_contour)
            return (x + w // 2, y + h // 2)

    def analyze_strawberry_shape(self, strawberry_mask):
        """分析草莓形状特征"""
        try:
            contours, _ = cv2.findContours(strawberry_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            if not contours:
                return None

            largest_contour = max(contours, key=cv2.contourArea)
            x, y, w, h = cv2.boundingRect(largest_contour)

            # 计算形状特征
            area = cv2.contourArea(largest_contour)
            perimeter = cv2.arcLength(largest_contour, True)
            aspect_ratio = w / h if h > 0 else 1.0
            extent = area / (w * h) if w * h > 0 else 0

            # 计算圆形度
            circularity = 4 * np.pi * area / (perimeter * perimeter) if perimeter > 0 else 0

            shape_info = {
                'size': (w, h),
                'area': area,
                'aspect_ratio': aspect_ratio,
                'circularity': circularity,
                'extent': extent,
                'center': (x + w//2, y + h//2)
            }

            print(f"  🍓 草莓形状分析: 尺寸({w}x{h}), 长宽比{aspect_ratio:.2f}, 圆形度{circularity:.2f}")
            return shape_info

        except Exception as e:
            print(f"分析草莓形状时出错: {e}")
            return None

    def find_largest_leaf_top_point(self, leaf_masks):
        """找到画面内面积最大的叶片的最顶端点"""
        try:
            if not leaf_masks:
                print("  ❌ 画面内无叶片")
                return None, 0

            largest_area = 0
            largest_leaf_top_point = None

            print(f"  🔍 检测到 {len(leaf_masks)} 个叶片，正在寻找最大叶片...")

            for i, (leaf_box, leaf_mask) in enumerate(leaf_masks.items()):
                leaf_area = np.sum(leaf_mask > 0)

                if leaf_area > largest_area:
                    largest_area = leaf_area
                    # 获取叶片顶点
                    leaf_pixels = np.where(leaf_mask > 0)
                    if len(leaf_pixels[0]) > 0:
                        min_y = np.min(leaf_pixels[0])
                        top_row_pixels = np.where(leaf_pixels[0] == min_y)[0]
                        x_coords_at_top = leaf_pixels[1][top_row_pixels]
                        top_x = int(np.mean(x_coords_at_top))
                        largest_leaf_top_point = (top_x, int(min_y))

                print(f"    叶片{i+1}: 面积={leaf_area:,}像素")

            if largest_leaf_top_point:
                print(f"  🌿 最大叶片面积: {largest_area:,}像素")
                print(f"  🔝 叶片顶点: ({largest_leaf_top_point[0]}, {largest_leaf_top_point[1]})")
                return largest_leaf_top_point, largest_area
            else:
                print("  ❌ 未找到有效的叶片顶点")
                return None, 0

        except Exception as e:
            print(f"寻找最大叶片顶点时出错: {e}")
            return None, 0

    def calculate_airflow_direction(self, strawberry_center, leaf_point):
        """计算气吹方向"""
        try:
            if not leaf_point:
                print("  ❌ 无叶片顶点数据，使用默认气吹方向")
                return {'servo_angle': 135, 'airflow_direction': 'default_upward'}

            # 计算从草莓中心到叶片顶点的向量
            dx = leaf_point[0] - strawberry_center[0]
            dy = leaf_point[1] - strawberry_center[1]

            # 计算角度（弧度转角度）
            angle_radians = math.atan2(dy, dx)
            angle_degrees = angle_radians * 180 / math.pi

            # 确保角度在0-360度范围内
            if angle_degrees < 0:
                angle_degrees += 360

            # 转换为舵机角度（90-180度范围）
            if 0 <= angle_degrees <= 90:  # 右下象限
                servo_angle = 135 + (angle_degrees / 90) * 45  # 135-180
            elif 90 < angle_degrees <= 180:  # 左下象限
                servo_angle = 180 - ((angle_degrees - 90) / 90) * 45  # 180-135
            elif 180 < angle_degrees <= 270:  # 左上象限
                servo_angle = 135 - ((angle_degrees - 180) / 90) * 45  # 135-90
            else:  # 右上象限 (270-360)
                servo_angle = 90 + ((angle_degrees - 270) / 90) * 45  # 90-135

            servo_angle = np.clip(servo_angle, 90, 180)

            print(f"  📍 草莓中心: ({strawberry_center[0]}, {strawberry_center[1]})")
            print(f"  🔝 叶片顶点: ({leaf_point[0]}, {leaf_point[1]})")
            print(f"  📐 指向角度: {angle_degrees:.1f}°")
            print(f"  ⚙️ 舵机角度: {servo_angle:.1f}°")

            return {
                'strawberry_center': strawberry_center,
                'leaf_center': leaf_point,
                'direction_angle': angle_degrees,
                'servo_angle': servo_angle,
                'airflow_direction': f"向叶片顶点 {angle_degrees:.1f}°"
            }

        except Exception as e:
            print(f"计算气吹方向时出错: {e}")
            return {'servo_angle': 135, 'airflow_direction': 'error_default'}

    def calculate_cnn_enhanced_air_blow_params(self, strawberry_mask, obstacle_mask, leaf_masks=None, frame=None):
        """
        CNN增强的螺旋引导气吹参数计算 - 核心算法
        """
        try:
            print("🤖 开始CNN增强的螺旋引导分析...")

            # 找到完整草莓的中心点
            complete_strawberry_mask = strawberry_mask + obstacle_mask
            strawberry_center = self.find_strawberry_center(complete_strawberry_mask)

            # 分析草莓形状
            strawberry_shape_info = self.analyze_strawberry_shape(complete_strawberry_mask)

            # 生成自适应螺旋路径
            spiral_path, spiral_params = self.generate_adaptive_spiral_path(
                strawberry_center, strawberry_shape_info
            )

            # 分析螺旋路径特性
            if frame is not None:
                path_characteristics = self.analyze_spiral_path_characteristics(frame, spiral_path)

                # 螺旋引导的区域生长
                grown_mask, adjusted_params, grown_pixels = self.spiral_guided_region_growing(
                    frame, spiral_path, path_characteristics
                )
            else:
                grown_mask = np.zeros(strawberry_mask.shape, dtype=np.uint8)
                adjusted_params = self.region_growing_params.copy()
                grown_pixels = 0
                path_characteristics = {'stats': {}}

            # 计算初始遮挡率
            obstacle_area = np.sum(obstacle_mask > 0)
            strawberry_area = np.sum(strawberry_mask > 0)
            total_area = strawberry_area + obstacle_area

            if total_area > 0:
                initial_occlusion_ratio = obstacle_area / total_area
            else:
                initial_occlusion_ratio = 0

            # 🤖 使用CNN回归器预测残留遮挡率
            predicted_residual_ratio = self.predict_residual_occlusion(
                path_characteristics, spiral_params, strawberry_shape_info
            )

            # 🎯 基于CNN预测结果优化气吹参数
            optimization_results = self.optimize_airflow_parameters(
                initial_occlusion_ratio, predicted_residual_ratio,
                strawberry_area, obstacle_area
            )

            # 收集训练数据（用于持续学习）
            if len(self.trainer.training_data) < 100:  # 限制训练数据量
                self.trainer.collect_training_sample(
                    path_characteristics, spiral_params, strawberry_shape_info,
                    initial_occlusion_ratio, predicted_residual_ratio
                )

            # 找到画面内面积最大的叶片顶点
            largest_leaf_top_point = None
            largest_leaf_area = 0
            if leaf_masks:
                largest_leaf_top_point, largest_leaf_area = self.find_largest_leaf_top_point(leaf_masks)

            # 计算气吹方向
            airflow_data = self.calculate_airflow_direction(strawberry_center, largest_leaf_top_point)
            servo_angle = airflow_data['servo_angle']

            # 使用优化后的气吹强度
            optimized_intensity = optimization_results['optimized_intensity']

            print(f"✅ CNN增强分析完成:")
            print(f"  初始遮挡率: {initial_occlusion_ratio:.3f}")
            print(f"  预测残留率: {predicted_residual_ratio:.3f}")
            print(f"  预期去除效果: {optimization_results['expected_removal_ratio']:.1%}")
            print(f"  优化气吹强度: {optimized_intensity:.1f}%")

            return {
                'center_point': strawberry_center,
                'servo_angle': servo_angle,
                'blow_intensity': optimized_intensity,
                'initial_occlusion_ratio': initial_occlusion_ratio,
                'predicted_residual_ratio': predicted_residual_ratio,
                'expected_removal_ratio': optimization_results['expected_removal_ratio'],
                'duration': optimization_results['duration'],
                'strawberry_area': strawberry_area,
                'obstacle_area': obstacle_area,
                'total_area': total_area,
                'leaf_center': largest_leaf_top_point,
                'largest_leaf_area': largest_leaf_area,
                'airflow_data': airflow_data,
                'spiral_path': spiral_path[:20],  # 只保留前20个点用于可视化
                'grown_mask': grown_mask,
                'grown_pixels': grown_pixels,
                'adjusted_params': adjusted_params,
                'strawberry_shape_info': strawberry_shape_info,
                'spiral_params': spiral_params,
                'optimization_results': optimization_results,
                'cnn_prediction': True
            }
        except Exception as e:
            print(f"CNN增强气吹参数计算时出错: {e}")
            return None

    def visualize_cnn_results(self, frame, params, position):
        """可视化CNN增强的螺旋引导区域生长结果"""
        if params is None:
            return frame

        x1, y1, x2, y2 = position

        # 获取参数
        servo_angle = params['servo_angle']
        blow_intensity = params['blow_intensity']
        strawberry_center = params['center_point']
        leaf_center = params.get('leaf_center')
        spiral_path = params.get('spiral_path', [])
        grown_mask = params.get('grown_mask')

        # 绘制螺旋路径
        if len(spiral_path) > 1:
            spiral_points = np.array(spiral_path, dtype=np.int32).reshape(-1, 1, 2)
            cv2.polylines(frame, [spiral_points], isClosed=False, color=(255, 0, 255), thickness=2)

        # 绘制区域生长结果
        if grown_mask is not None and np.sum(grown_mask) > 0:
            # 创建彩色掩膜叠加
            mask_overlay = np.zeros_like(frame)
            mask_overlay[grown_mask > 0] = [0, 255, 255]  # 青色表示生长区域
            frame = cv2.addWeighted(frame, 0.8, mask_overlay, 0.2, 0)

            # 绘制生长区域轮廓
            contours, _ = cv2.findContours(grown_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            cv2.drawContours(frame, contours, -1, (0, 255, 255), 2)

        # 绘制关键点
        cv2.circle(frame, strawberry_center, 6, (0, 255, 0), -1)  # 绿色 - 草莓中心

        if leaf_center:
            cv2.circle(frame, leaf_center, 4, (255, 0, 0), -1)  # 蓝色 - 叶片顶点

            # 绘制气吹方向箭头
            dx = leaf_center[0] - strawberry_center[0]
            dy = leaf_center[1] - strawberry_center[1]
            distance = math.sqrt(dx*dx + dy*dy)

            if distance > 0:
                arrow_length = 60
                unit_dx = dx / distance
                unit_dy = dy / distance

                arrow_end = (
                    int(strawberry_center[0] + unit_dx * arrow_length),
                    int(strawberry_center[1] + unit_dy * arrow_length)
                )

                cv2.arrowedLine(frame, strawberry_center, arrow_end, (0, 255, 255), 4)

        # 显示CNN增强的详细参数信息
        initial_occlusion = params.get('initial_occlusion_ratio', 0) * 100
        predicted_residual = params.get('predicted_residual_ratio', 0) * 100
        expected_removal = params.get('expected_removal_ratio', 0) * 100
        duration = params.get('duration', 0)

        text_lines = [
            f"🤖 CNN Enhanced Airflow Control",
            f"Servo: {servo_angle:.1f}°, Power: {blow_intensity:.1f}%",
            f"Initial: {initial_occlusion:.1f}% -> Predicted: {predicted_residual:.1f}%",
            f"Removal: {expected_removal:.1f}%, Duration: {duration:.1f}s"
        ]

        for i, text in enumerate(text_lines):
            y_pos = y1 - 85 + i * 20
            color = (0, 255, 255) if i == 0 else (255, 255, 255)
            cv2.putText(frame, text, (x1, y_pos), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

        return frame

    def draw_statistics(self, frame, width, height, frame_count, total_frames):
        """绘制统计信息"""
        stats_x = width - 400
        text_color = (0, 255, 0)
        shadow_color = (0, 0, 0)

        # 标题
        cv2.putText(frame, "🤖 CNN Enhanced Airflow System:", (stats_x, 40),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, shadow_color, 3)
        cv2.putText(frame, "🤖 CNN Enhanced Airflow System:", (stats_x, 40),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, text_color, 2)

        # 草莓总数
        cv2.putText(frame, f"Total Strawberries: {self.total_count}",
                    (stats_x, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.5, shadow_color, 3)
        cv2.putText(frame, f"Total Strawberries: {self.total_count}",
                    (stats_x, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.5, text_color, 2)

        # 已分析数量
        analyzed_count = len(self.strawberry_analysis_results)
        cv2.putText(frame, f"CNN Analyzed: {analyzed_count}",
                    (stats_x, 95), cv2.FONT_HERSHEY_SIMPLEX, 0.5, shadow_color, 3)
        cv2.putText(frame, f"CNN Analyzed: {analyzed_count}",
                    (stats_x, 95), cv2.FONT_HERSHEY_SIMPLEX, 0.5, text_color, 2)

        # 训练样本数
        training_samples = len(self.trainer.training_data)
        cv2.putText(frame, f"Training Samples: {training_samples}",
                    (stats_x, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.5, shadow_color, 3)
        cv2.putText(frame, f"Training Samples: {training_samples}",
                    (stats_x, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.5, text_color, 2)

        # 模型状态
        model_status = "Loaded" if self.cnn_regressor is not None else "Training"
        cv2.putText(frame, f"CNN Model: {model_status}",
                    (stats_x, 145), cv2.FONT_HERSHEY_SIMPLEX, 0.5, shadow_color, 3)
        cv2.putText(frame, f"CNN Model: {model_status}",
                    (stats_x, 145), cv2.FONT_HERSHEY_SIMPLEX, 0.5, text_color, 2)

        # 进度信息
        progress = (frame_count / total_frames) * 100
        progress_text = f"Progress: {frame_count}/{total_frames} ({progress:.1f}%)"
        cv2.putText(frame, progress_text, (10, height - 30),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, shadow_color, 3)
        cv2.putText(frame, progress_text, (10, height - 30),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, text_color, 2)

        # 图例
        legend_y = height - 180
        cv2.putText(frame, "Legend:", (10, legend_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
        cv2.putText(frame, "Green: Strawberry Center", (10, legend_y + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 2)
        cv2.putText(frame, "Blue: Leaf Top Point", (10, legend_y + 40), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 0), 2)
        cv2.putText(frame, "Magenta: Spiral Path", (10, legend_y + 60), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 255), 2)
        cv2.putText(frame, "Cyan: Grown Region", (10, legend_y + 80), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 0), 2)
        cv2.putText(frame, "Yellow Arrow: Airflow", (10, legend_y + 100), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 2)
        cv2.putText(frame, "🤖 CNN Prediction", (10, legend_y + 120), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 2)

    def process_video(self):
        """主要的视频处理方法 - CNN增强版本"""
        cap = cv2.VideoCapture(self.input_path)
        width, height = int(cap.get(3)), int(cap.get(4))
        self.set_valid_region(width)

        fps = cap.get(5)
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(self.output_path, fourcc, fps, (width, height))

        frame_count = 0
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        print(f"视频信息: {width}x{height}, {fps:.2f}FPS, 总帧数: {total_frames}")
        print(f"检测类别: {self.class_names[0]} (草莓), {self.class_names[1]} (叶片), {self.class_names[2]} (遮挡掩膜)")
        print("按 'q' 键退出实时预览")
        print("🤖 CNN增强螺旋引导区域生长智能气吹控制系统")
        print("🎯 通过CNN回归器预测残留遮挡率，优化气吹参数")
        print("🍓 基于机器学习的智能气吹控制")

        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                print("视频处理完成")
                break

            frame_count += 1

            # 绘制有效区域边界
            overlay = frame.copy()
            cv2.rectangle(overlay,
                          (self.valid_region[0], 0),
                          (self.valid_region[1], height),
                          (0, 255, 255), -1)
            cv2.addWeighted(overlay, 0.1, frame, 0.9, 0, frame)

            # YOLOv8-Seg预测
            results = self.model.predict(
                source=frame,
                imgsz=640,
                conf=0.5,
                iou=0.5,
                device=self.device,
                verbose=False
            )

            # 分别处理草莓(类别0)、叶片(类别1)和遮挡掩膜(类别2)
            strawberry_detections = []
            strawberry_masks = []
            obstacle_masks = {}
            leaf_masks = {}

            for result in results:
                if result.masks is None:
                    continue

                boxes = result.boxes.xywh.cpu().numpy()
                confs = result.boxes.conf.cpu().numpy()
                clss = result.boxes.cls.cpu().numpy().astype(int)
                masks = result.masks.data.cpu().numpy()

                for box, conf, cls_id, mask in zip(boxes, confs, clss, masks):
                    x_center = box[0]
                    if self.is_in_valid_region(x_center):
                        if cls_id == 0:  # 草莓
                            strawberry_detections.append((box, conf, cls_id))
                            strawberry_masks.append(mask)
                        elif cls_id == 1:  # 叶片
                            if mask.shape[0] != height or mask.shape[1] != width:
                                mask_resized = cv2.resize(mask, (width, height))
                            else:
                                mask_resized = mask
                            leaf_masks[tuple(box)] = (mask_resized > 0.5).astype(np.uint8) * 255
                        elif cls_id == 2:  # 遮挡掩膜
                            if mask.shape[0] != height or mask.shape[1] != width:
                                mask_resized = cv2.resize(mask, (width, height))
                            else:
                                mask_resized = mask
                            obstacle_masks[tuple(box)] = (mask_resized > 0.5).astype(np.uint8) * 255

            # DeepSORT跟踪草莓
            if strawberry_detections:
                bboxes = torch.Tensor(np.array([d[0] for d in strawberry_detections]))
                confs = torch.Tensor(np.array([d[1] for d in strawberry_detections]))
                clss = torch.Tensor(np.array([d[2] for d in strawberry_detections]))
                tracks = self.tracker.update(bboxes, confs, clss, frame)
            else:
                tracks = []

            # 处理跟踪结果并进行CNN增强分析
            for i, track in enumerate(tracks):
                x1, y1, x2, y2, cls_id, track_id = map(int, track[:6])
                x_center = (x1 + x2) // 2
                y_center = (y1 + y2) // 2

                if self.is_in_valid_region(x_center):
                    # 获取稳定ID
                    stable_id = self.get_stable_id(track_id, (x_center, y_center), frame_count)

                    # 更新统计信息
                    if stable_id not in self.recorded_ids:
                        self.total_count += 1
                        self.recorded_ids.add(stable_id)
                        print(f"新草莓检测: 稳定ID {stable_id}, 当前总数: {self.total_count}")

                    # 获取对应的草莓掩膜
                    if i < len(strawberry_masks):
                        strawberry_mask = strawberry_masks[i]
                        if strawberry_mask.shape[0] != height or strawberry_mask.shape[1] != width:
                            strawberry_mask = cv2.resize(strawberry_mask, (width, height))
                        strawberry_mask = (strawberry_mask > 0.5).astype(np.uint8) * 255

                        # 寻找最近的遮挡掩膜
                        closest_obstacle_mask = None
                        min_obstacle_distance = float('inf')

                        for obstacle_box, obstacle_mask in obstacle_masks.items():
                            obs_x_center = obstacle_box[0]
                            obs_y_center = obstacle_box[1]
                            distance = np.sqrt((x_center - obs_x_center)**2 + (y_center - obs_y_center)**2)
                            if distance < min_obstacle_distance and distance < 100:
                                min_obstacle_distance = distance
                                closest_obstacle_mask = obstacle_mask

                        # 进行CNN增强的气吹分析
                        if closest_obstacle_mask is not None:
                            print(f"\n🤖 开始CNN增强分析草莓ID {stable_id}...")
                            air_blow_params = self.calculate_cnn_enhanced_air_blow_params(
                                strawberry_mask, closest_obstacle_mask, leaf_masks, frame
                            )
                            if air_blow_params:
                                self.strawberry_analysis_results[stable_id] = air_blow_params

                                # 输出详细信息
                                initial_occlusion = air_blow_params['initial_occlusion_ratio'] * 100
                                predicted_residual = air_blow_params['predicted_residual_ratio'] * 100
                                expected_removal = air_blow_params['expected_removal_ratio'] * 100

                                print(f"草莓ID {stable_id}: 初始遮挡 {initial_occlusion:.1f}% -> 预测残留 {predicted_residual:.1f}%")
                                print(f"  预期去除效果: {expected_removal:.1f}%, 优化气吹强度: {air_blow_params['blow_intensity']:.1f}%")
                                print(f"  建议持续时间: {air_blow_params['duration']:.1f}秒")

                                # 可视化CNN增强结果
                                frame = self.visualize_cnn_results(frame, air_blow_params, (x1, y1, x2, y2))

                    # 获取或生成颜色
                    if stable_id not in self.color_dict:
                        self.color_dict[stable_id] = tuple(np.random.randint(0, 255, 3).tolist())
                    color = self.color_dict[stable_id]

                    # 绘制边界框和稳定ID
                    cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
                    cv2.putText(frame, f"🍓 ID:{stable_id}",
                                (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

            # 绘制分割掩膜
            overlay = frame.copy()

            # 绘制草莓分割掩膜（绿色）
            for mask in strawberry_masks:
                if mask.shape[0] != height or mask.shape[1] != width:
                    mask = cv2.resize(mask, (width, height))
                mask_binary = (mask > 0.5).astype(np.uint8)
                overlay[mask_binary == 1] = [0, 255, 0]

            # 绘制叶片掩膜（蓝色）
            for leaf_mask in leaf_masks.values():
                overlay[leaf_mask > 0] = [255, 0, 0]

            # 绘制遮挡掩膜（红色）
            for obstacle_mask in obstacle_masks.values():
                overlay[obstacle_mask > 0] = [0, 0, 255]

            frame = cv2.addWeighted(overlay, self.alpha, frame, 1 - self.alpha, 0)

            # 显示统计信息
            self.draw_statistics(frame, width, height, frame_count, total_frames)

            # 实时显示窗口
            cv2.imshow('🤖 CNN Enhanced Airflow System', frame)

            # 保存到输出视频
            out.write(frame)

            # 按键控制
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                print(f"用户退出，已处理 {frame_count}/{total_frames} 帧")
                break

            # 每50帧打印一次进度
            if frame_count % 50 == 0:
                print(f"处理进度: {frame_count}/{total_frames} ({frame_count/total_frames*100:.1f}%)")
                print(f"当前统计: 草莓总数: {self.total_count}, CNN分析: {len(self.strawberry_analysis_results)}")
                print(f"训练样本: {len(self.trainer.training_data)}")

        cap.release()
        out.release()
        cv2.destroyAllWindows()

        # 训练CNN模型（如果收集了足够的数据）
        if len(self.trainer.training_data) >= 20 and self.cnn_regressor is None:
            print("\n🤖 开始训练CNN回归器...")
            trained_model = self.trainer.train_model(epochs=50, batch_size=8)
            if trained_model:
                self.cnn_regressor = trained_model
                print("✅ CNN回归器训练完成")

        print(f"\n处理完成! 共处理 {frame_count} 帧")
        print(f"输出视频保存至: {self.output_path}")
        print(f"检测到草莓总数: {self.total_count}")
        print(f"完成CNN增强分析的草莓数: {len(self.strawberry_analysis_results)}")
        print(f"收集的训练样本数: {len(self.trainer.training_data)}")


if __name__ == "__main__":
    # 配置参数
    config = {
        "input": r"F:\chuzhendangdaima\yolov5-deepsort-pedestraintracking\视频\699b2d9941c677a2fc210350cce01c53.mp4",
        "output": "output2/v19_cnn_enhanced_airflow_result.mp4",
        "weights": "weights/best.pt"
    }

    # 创建CNN增强螺旋引导系统实例
    tracker = SpiralGuidedCNNAirflowSystem(config)

    print("=" * 120)
    print("🤖 CNN增强螺旋引导区域生长智能气吹控制系统 v19_cnn_regressor_airflow_system 🤖")
    print("=" * 120)
    print("核心创新功能:")
    print("✅ YOLOv8-seg 分割果实与遮挡物，计算初始遮挡率")
    print("✅ 螺旋引导区域生长算法 生成候选气吹区域，提取螺旋路径特征")
    print("🤖 轻量级CNN回归器 以螺旋特征为输入，预测气吹后的残留遮挡率")
    print("✅ DeepSORT 跟踪果实，确保遮挡率计算时序一致性")
    print("=" * 120)
    print("🤖 CNN回归器特性:")
    print("  • 1D卷积神经网络架构，专门处理螺旋路径特征序列")
    print("  • 64维特征向量输入，包含灰度、梯度、纹理、形状等多维特征")
    print("  • 自适应特征提取和批量归一化")
    print("  • 在线学习能力，持续收集数据并优化模型")
    print("  • 基于CNN预测结果的智能气吹参数优化")
    print("=" * 120)
    print("🎯 气吹参数优化策略:")
    print("  • 预测残留遮挡率 -> 计算预期去除效果")
    print("  • 根据预期效果调整气吹强度（节能或增强）")
    print("  • 基于遮挡面积和复杂度计算持续时间")
    print("  • 实时反馈学习，提升预测准确性")
    print("=" * 120)

    print(f"[{datetime.now().strftime('%H:%M:%S')}] 🤖 CNN增强系统启动")
    tracker.process_video()
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 🤖 CNN增强系统完成")

    # 输出最终分析结果
    print("\n" + "=" * 120)
    print("📊 CNN增强螺旋引导区域生长最终分析结果")
    print("=" * 120)
    print(f"检测到的草莓总数: {tracker.total_count}")
    print(f"完成CNN增强分析的草莓: {len(tracker.strawberry_analysis_results)}")
    print(f"收集的训练样本数: {len(tracker.trainer.training_data)}")
    print(f"CNN模型状态: {'已加载' if tracker.cnn_regressor is not None else '需要训练'}")

    if tracker.strawberry_analysis_results:
        print("\n🤖 CNN增强气吹控制参数:")
        print("-" * 100)

        # 统计预测效果
        total_initial_occlusion = 0
        total_predicted_residual = 0
        total_expected_removal = 0

        for stable_id, params in tracker.strawberry_analysis_results.items():
            print(f"草莓ID {stable_id}:")
            print(f"  🎯 初始遮挡率: {params['initial_occlusion_ratio']:.3f} ({params['initial_occlusion_ratio']*100:.1f}%)")
            print(f"  🤖 CNN预测残留率: {params['predicted_residual_ratio']:.3f} ({params['predicted_residual_ratio']*100:.1f}%)")
            print(f"  📈 预期去除效果: {params['expected_removal_ratio']:.3f} ({params['expected_removal_ratio']*100:.1f}%)")
            print(f"  ⚙️ 优化气吹强度: {params['blow_intensity']:.1f}%")
            print(f"  ⏱️ 建议持续时间: {params['duration']:.1f}秒")
            print(f"  🎮 舵机角度: {params['servo_angle']:.1f}° (基于叶片顶点计算)")

            # 累计统计
            total_initial_occlusion += params['initial_occlusion_ratio']
            total_predicted_residual += params['predicted_residual_ratio']
            total_expected_removal += params['expected_removal_ratio']

            # 螺旋引导特有信息
            print(f"  🌀 生长像素数: {params.get('grown_pixels', 0):,}")

            if params.get('strawberry_shape_info'):
                shape_info = params['strawberry_shape_info']
                print(f"  🍓 草莓尺寸: {shape_info.get('size', 'N/A')}")
                print(f"  📐 长宽比: {shape_info.get('aspect_ratio', 0):.2f}")

            if params.get('leaf_center'):
                print(f"  🌿 叶片顶点: ({params['leaf_center'][0]}, {params['leaf_center'][1]})")
                print(f"  🌿 叶片面积: {params.get('largest_leaf_area', 0):,} 像素")
            else:
                print(f"  🌿 叶片数据: 未检测到叶片，使用默认方向")

            print(f"  📊 草莓面积: {params['strawberry_area']:,} 像素")
            print(f"  📊 遮挡面积: {params['obstacle_area']:,} 像素")
            print("-" * 80)

        # 整体统计
        if len(tracker.strawberry_analysis_results) > 0:
            avg_initial = total_initial_occlusion / len(tracker.strawberry_analysis_results)
            avg_predicted = total_predicted_residual / len(tracker.strawberry_analysis_results)
            avg_removal = total_expected_removal / len(tracker.strawberry_analysis_results)

            print(f"\n📈 整体预测效果统计:")
            print(f"  平均初始遮挡率: {avg_initial:.3f} ({avg_initial*100:.1f}%)")
            print(f"  平均预测残留率: {avg_predicted:.3f} ({avg_predicted*100:.1f}%)")
            print(f"  平均预期去除效果: {avg_removal:.3f} ({avg_removal*100:.1f}%)")

    print(f"\n🎯 总共创建了 {tracker.next_stable_id - 1} 个稳定ID")
    print(f"📹 输出视频: {config['output']}")
    print("🤖 核心算法: CNN增强螺旋引导区域生长")
    print("🎯 创新点: 轻量级CNN回归器预测残留遮挡率，智能优化气吹参数")
    print("🍓 智能分析: 基于机器学习的气吹效果预测和参数优化")
    print("🌿 气吹方向: 草莓中心指向画面内最大叶片顶点")
    print("📚 持续学习: 在线收集训练数据，不断提升预测准确性")
    print("=" * 120)
